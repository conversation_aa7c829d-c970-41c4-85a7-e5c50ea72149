package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.data.model.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.MonitoringType
import dev.pigmomo.yhkit2025.data.model.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.service.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import kotlinx.coroutines.launch

/**
 * 添加监控计划对话框
 * @param onDismiss 关闭对话框回调
 * @param onPlanAdded 监控计划添加成功回调
 * @param availableAccounts 可用的账户列表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddMonitoringPlanDialog(
    onDismiss: () -> Unit,
    onPlanAdded: () -> Unit,
    availableAccounts: List<OrderTokenEntity>
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // 表单状态
    var planName by remember { mutableStateOf("") }
    var selectedMonitoringType by remember { mutableStateOf(MonitoringType.CART) }
    var selectedOperationType by remember { mutableStateOf(MonitoringOperationType.INTERVAL) }
    var selectedAccount by remember { mutableStateOf<OrderTokenEntity?>(null) }
    var productIds by remember { mutableStateOf("") }
    var intervalSeconds by remember { mutableStateOf("300") }
    var priority by remember { mutableStateOf("5") }
    var isEnabled by remember { mutableStateOf(true) }
    
    // 验证状态
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }
    
    // 获取服务实例
    val monitoringPlanRepository = remember { MonitoringServiceManager.getMonitoringPlanRepository(context) }
    
    // 初始化默认账户
    LaunchedEffect(availableAccounts) {
        if (selectedAccount == null && availableAccounts.isNotEmpty()) {
            selectedAccount = availableAccounts.first()
        }
    }
    
    // 验证表单
    val isFormValid = planName.isNotBlank() && 
                     selectedAccount != null && 
                     productIds.isNotBlank() &&
                     intervalSeconds.toIntOrNull() != null &&
                     priority.toIntOrNull() != null
    
    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            TextButton(
                onClick = {
                    if (isFormValid && selectedAccount != null) {
                        scope.launch {
                            isLoading = true
                            errorMessage = ""
                            
                            try {
                                val productIdList = productIds.split(",")
                                    .map { it.trim() }
                                    .filter { it.isNotBlank() }
                                
                                val newPlan = MonitoringPlanEntity(
                                    name = planName,
                                    type = selectedMonitoringType,
                                    account = selectedAccount!!,
                                    productIds = productIdList,
                                    isEnabled = isEnabled,
                                    operationType = selectedOperationType,
                                    intervalSeconds = intervalSeconds.toInt(),
                                    priority = priority.toInt()
                                )
                                
                                val success = monitoringPlanRepository.addMonitoringPlan(newPlan)
                                if (success) {
                                    onPlanAdded()
                                    onDismiss()
                                } else {
                                    errorMessage = "添加监控计划失败"
                                }
                            } catch (e: Exception) {
                                errorMessage = "添加监控计划时发生错误: ${e.message}"
                            } finally {
                                isLoading = false
                            }
                        }
                    }
                },
                enabled = isFormValid && !isLoading
            ) {
                if (isLoading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("添加")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "添加监控计划",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )
                
                IconButton(onClick = onDismiss) {
                    Icon(Icons.Default.Close, contentDescription = "关闭")
                }
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(500.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 错误信息显示
                if (errorMessage.isNotBlank()) {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f))
                    ) {
                        Text(
                            text = errorMessage,
                            color = Color.Red,
                            modifier = Modifier.padding(12.dp),
                            fontSize = 14.sp
                        )
                    }
                }
                
                // 计划名称
                OutlinedTextField(
                    value = planName,
                    onValueChange = { planName = it },
                    label = { Text("计划名称") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    shape = RoundedCornerShape(8.dp)
                )
                
                // 监控类型选择
                Text(
                    text = "监控类型",
                    fontWeight = FontWeight.Medium,
                    fontSize = 16.sp
                )
                
                Column(modifier = Modifier.selectableGroup()) {
                    MonitoringType.values().forEach { type ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (selectedMonitoringType == type),
                                    onClick = { selectedMonitoringType = type },
                                    role = Role.RadioButton
                                )
                                .padding(horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (selectedMonitoringType == type),
                                onClick = null
                            )
                            Text(
                                text = when (type) {
                                    MonitoringType.CART -> "购物车监控"
                                    MonitoringType.PRODUCT_DETAIL -> "商品详情监控"
                                },
                                modifier = Modifier.padding(start = 16.dp)
                            )
                        }
                    }
                }
                
                // 操作类型选择
                Text(
                    text = "操作类型",
                    fontWeight = FontWeight.Medium,
                    fontSize = 16.sp
                )
                
                Column(modifier = Modifier.selectableGroup()) {
                    MonitoringOperationType.values().forEach { type ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .selectable(
                                    selected = (selectedOperationType == type),
                                    onClick = { selectedOperationType = type },
                                    role = Role.RadioButton
                                )
                                .padding(horizontal = 16.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = (selectedOperationType == type),
                                onClick = null
                            )
                            Text(
                                text = when (type) {
                                    MonitoringOperationType.INTERVAL -> "间隔监控"
                                    MonitoringOperationType.SCHEDULED -> "定时监控"
                                    MonitoringOperationType.MANUAL -> "手动监控"
                                },
                                modifier = Modifier.padding(start = 16.dp)
                            )
                        }
                    }
                }
                
                // 账户选择
                if (availableAccounts.isNotEmpty()) {
                    Text(
                        text = "选择账户",
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp
                    )
                    
                    var expanded by remember { mutableStateOf(false) }
                    
                    ExposedDropdownMenuBox(
                        expanded = expanded,
                        onExpandedChange = { expanded = !expanded }
                    ) {
                        OutlinedTextField(
                            value = selectedAccount?.phoneNumber ?: "",
                            onValueChange = {},
                            readOnly = true,
                            label = { Text("账户") },
                            trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .menuAnchor(),
                            shape = RoundedCornerShape(8.dp)
                        )
                        
                        ExposedDropdownMenu(
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            availableAccounts.forEach { account ->
                                DropdownMenuItem(
                                    text = { 
                                        Column {
                                            Text(account.phoneNumber)
                                            Text(
                                                text = "UID: ${account.uid}",
                                                fontSize = 12.sp,
                                                color = Color.Gray
                                            )
                                        }
                                    },
                                    onClick = {
                                        selectedAccount = account
                                        expanded = false
                                    }
                                )
                            }
                        }
                    }
                }

                // 商品ID列表
                OutlinedTextField(
                    value = productIds,
                    onValueChange = { productIds = it },
                    label = { Text("商品ID列表") },
                    placeholder = { Text("多个ID用逗号分隔，如：123,456,789") },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 2,
                    maxLines = 4,
                    shape = RoundedCornerShape(8.dp)
                )

                // 间隔时间（仅间隔监控时显示）
                if (selectedOperationType == MonitoringOperationType.INTERVAL) {
                    OutlinedTextField(
                        value = intervalSeconds,
                        onValueChange = { intervalSeconds = it },
                        label = { Text("监控间隔（秒）") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        shape = RoundedCornerShape(8.dp)
                    )
                }

                // 优先级
                OutlinedTextField(
                    value = priority,
                    onValueChange = { priority = it },
                    label = { Text("优先级（1-10）") },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    shape = RoundedCornerShape(8.dp)
                )

                // 是否启用
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Checkbox(
                        checked = isEnabled,
                        onCheckedChange = { isEnabled = it }
                    )
                    Text(
                        text = "启用监控",
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        },
        containerColor = dialogContainerColor(),
        modifier = Modifier
            .fillMaxWidth(0.95f)
            .fillMaxHeight(0.9f)
    )
}
