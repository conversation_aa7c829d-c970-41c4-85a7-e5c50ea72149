package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.ui.res.painterResource
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.MonitoringType
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.service.MonitoringServiceManager
import dev.pigmomo.yhkit2025.service.MonitoringTaskResult
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

/**
 * 监控任务列表弹窗
 * @param onDismiss 关闭弹窗回调
 * @param onShowAddDialog 显示添加监控计划对话框回调
 */
@Composable
fun MonitoringTaskDialog(
    onDismiss: () -> Unit,
    onShowAddDialog: () -> Unit = {}
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 状态管理
    var monitoringPlans by remember { mutableStateOf<List<MonitoringPlanEntity>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var isExecuting by remember { mutableStateOf(false) }
    var executionResults by remember { mutableStateOf<Map<Int, MonitoringTaskResult>>(emptyMap()) }
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    var showAddDialog by remember { mutableStateOf(false) }
    var availableAccounts by remember { mutableStateOf<List<OrderTokenEntity>>(emptyList()) }

    // 获取服务实例
    val monitoringPlanRepository =
        remember { MonitoringServiceManager.getMonitoringPlanRepository(context) }
    val schedulerService = remember { MonitoringServiceManager.getSchedulerService(context) }

    // 获取数据库实例来访问账户
    val database = remember { dev.pigmomo.yhkit2025.data.database.AppDatabase.getDatabase(context) }
    val orderTokenDao = remember { database.orderTokenDao() }

    // 标签页选项
    val tabs = listOf("全部", "间隔监控", "定时监控", "手动监控")

    // 加载监控计划和账户列表
    LaunchedEffect(Unit) {
        scope.launch {
            try {
                // 加载监控计划
                monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                    monitoringPlans = plans
                    isLoading = false
                }
            } catch (e: Exception) {
                isLoading = false
            }
        }

        scope.launch {
            try {
                // 加载可用账户
                orderTokenDao.getAllOrderTokens().collect { accounts ->
                    availableAccounts = accounts
                }
            } catch (e: Exception) {
                // 处理错误
            }
        }
    }

    // 根据选中的标签页过滤数据
    val filteredPlans = remember(monitoringPlans, selectedTabIndex) {
        when (selectedTabIndex) {
            0 -> monitoringPlans // 全部
            1 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.INTERVAL }
            2 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.SCHEDULED }
            3 -> monitoringPlans.filter { it.operationType == MonitoringOperationType.MANUAL }
            else -> monitoringPlans
        }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {},
        title = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "监控任务列表",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold
                )

                Row {
                    // 刷新按钮
                    IconButton(
                        onClick = {
                            scope.launch {
                                isLoading = true
                                try {
                                    monitoringPlanRepository.getAllMonitoringPlans()
                                        .collect { plans ->
                                            monitoringPlans = plans
                                            isLoading = false
                                        }
                                } catch (e: Exception) {
                                    isLoading = false
                                }
                            }
                        }
                    ) {
                        Icon(Icons.Default.Refresh, contentDescription = "刷新")
                    }

                    // 关闭按钮
                    IconButton(onClick = onDismiss) {
                        Icon(Icons.Default.Close, contentDescription = "关闭")
                    }
                }
            }
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(600.dp)
            ) {

                // 操作按钮栏
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = Color(0xCBDEE6F7))
                ) {
                    Row(
                        modifier = Modifier.padding(8.dp),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        TokenActionButton(
                            icon = R.drawable.baseline_notes_24,
                            text = "执行全部",
                            onClick = {
                                if (!isExecuting) {
                                    scope.launch {
                                        isExecuting = true
                                        try {
                                            val results =
                                                schedulerService.forceExecuteAllEnabledPlans()
                                            executionResults =
                                                results.associate { it.first.id to it.second }
                                        } catch (e: Exception) {
                                            // 处理错误
                                        } finally {
                                            isExecuting = false
                                        }
                                    }
                                }
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.outline_timer_24,
                            text = "启动调度",
                            onClick = {
                                if (!schedulerService.isSchedulerRunning()) {
                                    schedulerService.startScheduler()
                                }
                            }
                        )

                        TokenActionButton(
                            icon = R.drawable.outline_timer_off_24,
                            text = "停止调度",
                            onClick = {
                                if (schedulerService.isSchedulerRunning()) {
                                    schedulerService.stopScheduler()
                                }
                            }
                        )

                        // 增加监控按钮
                        TokenActionButton(
                            imageVector = Icons.Default.Add,
                            text = "增加监控",
                            onClick = { showAddDialog = true }
                        )


                    }
                }

                // 标签页
                TabRow(
                    selectedTabIndex = selectedTabIndex,
                    modifier = Modifier.padding(vertical = 8.dp)
                ) {
                    tabs.forEachIndexed { index, title ->
                        Tab(
                            selected = selectedTabIndex == index,
                            onClick = { selectedTabIndex = index },
                            text = { Text(title) }
                        )
                    }
                }

                // 任务列表
                if (isLoading) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                } else if (filteredPlans.isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "暂无监控任务",
                            color = Color.Gray
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(filteredPlans) { plan ->
                            MonitoringTaskItem(
                                plan = plan,
                                executionResult = executionResults[plan.id],
                                isExecuting = isExecuting,
                                onExecute = { selectedPlan ->
                                    scope.launch {
                                        try {
                                            val result =
                                                schedulerService.executeManualTask(selectedPlan.id)
                                            executionResults =
                                                executionResults + (selectedPlan.id to result)
                                        } catch (e: Exception) {
                                            // 处理错误
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
            }
        },
        containerColor = dialogContainerColor(),
        modifier = Modifier
            .fillMaxWidth(0.95f)
            .fillMaxHeight(0.8f)
    )

    // 显示添加监控计划对话框
    if (showAddDialog) {
        AddMonitoringPlanDialog(
            onDismiss = { showAddDialog = false },
            onPlanAdded = {
                // 刷新监控计划列表
                scope.launch {
                    try {
                        monitoringPlanRepository.getAllMonitoringPlans().collect { plans ->
                            monitoringPlans = plans
                        }
                    } catch (e: Exception) {
                        // 处理错误
                    }
                }
            },
            availableAccounts = availableAccounts
        )
    }
}

/**
 * 监控任务项组件
 */
@Composable
fun MonitoringTaskItem(
    plan: MonitoringPlanEntity,
    executionResult: MonitoringTaskResult?,
    isExecuting: Boolean,
    onExecute: (MonitoringPlanEntity) -> Unit
) {
    val dateFormat = remember { SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()) }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (plan.isEnabled) Color.White else Color.Gray.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            // 任务标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = plan.name,
                        fontWeight = FontWeight.Bold,
                        fontSize = 16.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Row {
                        Text(
                            text = when (plan.type) {
                                MonitoringType.CART -> "购物车"
                                MonitoringType.PRODUCT_DETAIL -> "商品详情"
                            },
                            fontSize = 12.sp,
                            color = Color.Gray
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = when (plan.operationType) {
                                MonitoringOperationType.INTERVAL -> "间隔监控"
                                MonitoringOperationType.SCHEDULED -> "定时监控"
                                MonitoringOperationType.MANUAL -> "手动监控"
                            },
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                    }
                }

                // 执行按钮
                IconButton(
                    onClick = { onExecute(plan) },
                    enabled = plan.isEnabled && !isExecuting
                ) {
                    Icon(
                        Icons.Default.PlayArrow,
                        contentDescription = "执行",
                        tint = if (plan.isEnabled) MaterialTheme.colorScheme.primary else Color.Gray
                    )
                }
            }

            // 任务详情
            Spacer(modifier = Modifier.height(8.dp))

            Row {
                Text(
                    text = "商品数量: ${plan.productIds.size}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = "优先级: ${plan.priority}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )

                Spacer(modifier = Modifier.width(16.dp))

                Text(
                    text = "执行次数: ${plan.executedCount}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 最后执行时间
            plan.lastExecutedAt?.let { lastExecuted ->
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "最后执行: ${dateFormat.format(lastExecuted)}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }

            // 执行结果
            executionResult?.let { result ->
                Spacer(modifier = Modifier.height(8.dp))
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = if (result.success) Color.Green.copy(alpha = 0.1f) else Color.Red.copy(
                            alpha = 0.1f
                        )
                    )
                ) {
                    Column(modifier = Modifier.padding(8.dp)) {
                        Text(
                            text = if (result.success) "✓ 执行成功" else "✗ 执行失败",
                            fontSize = 12.sp,
                            color = if (result.success) Color.Green else Color.Red,
                            fontWeight = FontWeight.Bold
                        )

                        Text(
                            text = result.message,
                            fontSize = 11.sp,
                            color = Color.Gray,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )

                        if (result.success && (result.changesDetected > 0 || result.importantChanges > 0)) {
                            Text(
                                text = "检测到 ${result.changesDetected} 个变化 (${result.importantChanges} 个重要)",
                                fontSize = 11.sp,
                                color = Color.Blue
                            )
                        }
                    }
                }
            }
        }
    }
}
