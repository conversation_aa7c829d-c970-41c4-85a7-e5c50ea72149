package dev.pigmomo.yhkit2025.utils.productmonitor

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import dev.pigmomo.yhkit2025.data.model.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.MonitoringPlanEntity
import java.util.Calendar
import java.util.Date

/**
 * 监控调度工具类
 * 处理不同类型的监控规则和调度逻辑
 */
object MonitoringScheduleUtils {

    private const val TAG = "MonitoringScheduleUtils"
    private val gson = Gson()

    /**
     * 定时配置数据类
     */
    data class ScheduledConfig(
        val hours: List<Int> = emptyList(),        // 小时列表 (0-23)
        val minutes: List<Int> = emptyList(),      // 分钟列表 (0-59)
        val weekdays: List<Int> = emptyList(),     // 星期列表 (1-7, 1=周一)
        val cron: String = "",                     // Cron表达式（可选）
        val timezone: String = "Asia/Shanghai"     // 时区
    )

    /**
     * 检查监控计划是否应该执行
     * @param plan 监控计划
     * @param currentTime 当前时间
     * @return 是否应该执行
     */
    fun shouldExecute(plan: MonitoringPlanEntity, currentTime: Date = Date()): Boolean {
        // 检查基本条件
        if (!plan.isEnabled) {
            Log.d(TAG, "监控计划 ${plan.name} 未启用")
            return false
        }

        // 检查执行次数限制
        if (plan.maxExecutions != -1 && plan.executedCount >= plan.maxExecutions) {
            Log.d(TAG, "监控计划 ${plan.name} 已达到最大执行次数")
            return false
        }

        // 检查时间范围
        if (!isWithinTimeRange(plan, currentTime)) {
            Log.d(TAG, "监控计划 ${plan.name} 不在执行时间范围内")
            return false
        }

        // 检查每日时间限制
        if (!isWithinDailyTimeRange(plan, currentTime)) {
            Log.d(TAG, "监控计划 ${plan.name} 不在每日执行时间范围内")
            return false
        }

        // 检查工作日限制
        if (!isValidWeekday(plan, currentTime)) {
            Log.d(TAG, "监控计划 ${plan.name} 不在有效工作日")
            return false
        }

        // 根据操作类型检查
        return when (plan.operationType) {
            MonitoringOperationType.INTERVAL -> shouldExecuteInterval(plan, currentTime)
            MonitoringOperationType.SCHEDULED -> shouldExecuteScheduled(plan, currentTime)
            MonitoringOperationType.MANUAL -> false // 手动执行不会自动触发
        }
    }

    /**
     * 检查间隔监控是否应该执行
     */
    private fun shouldExecuteInterval(plan: MonitoringPlanEntity, currentTime: Date): Boolean {
        val lastExecuted = plan.lastExecutedAt
        if (lastExecuted == null) {
            Log.d(TAG, "监控计划 ${plan.name} 首次执行")
            return true
        }

        val timeDiff = (currentTime.time - lastExecuted.time) / 1000 // 转换为秒
        val shouldExecute = timeDiff >= plan.intervalSeconds

        if (shouldExecute) {
            Log.d(TAG, "监控计划 ${plan.name} 间隔时间已到，应该执行")
        } else {
            Log.d(TAG, "监控计划 ${plan.name} 间隔时间未到，还需等待 ${plan.intervalSeconds - timeDiff} 秒")
        }

        return shouldExecute
    }

    /**
     * 检查定时监控是否应该执行
     */
    private fun shouldExecuteScheduled(plan: MonitoringPlanEntity, currentTime: Date): Boolean {
        if (plan.scheduledConfig.isEmpty()) {
            Log.w(TAG, "监控计划 ${plan.name} 的定时配置为空")
            return false
        }

        return try {
            val config = gson.fromJson(plan.scheduledConfig, ScheduledConfig::class.java)
            isScheduledTime(config, currentTime, plan.lastExecutedAt)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "解析监控计划 ${plan.name} 的定时配置失败", e)
            false
        }
    }

    /**
     * 检查是否为定时执行时间
     */
    private fun isScheduledTime(config: ScheduledConfig, currentTime: Date, lastExecuted: Date?): Boolean {
        val calendar = Calendar.getInstance()
        calendar.time = currentTime

        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = calendar.get(Calendar.MINUTE)
        val currentWeekday = calendar.get(Calendar.DAY_OF_WEEK)

        // 检查星期限制
        if (config.weekdays.isNotEmpty()) {
            val weekday = if (currentWeekday == Calendar.SUNDAY) 7 else currentWeekday - 1 // 转换为1-7格式
            if (!config.weekdays.contains(weekday)) {
                return false
            }
        }

        // 检查小时和分钟
        if (config.hours.isNotEmpty() && config.minutes.isNotEmpty()) {
            val isValidTime = config.hours.any { hour ->
                config.minutes.any { minute ->
                    currentHour == hour && currentMinute == minute
                }
            }

            if (!isValidTime) {
                return false
            }
        } else if (config.hours.isNotEmpty()) {
            // 只检查小时，分钟为0
            if (!config.hours.contains(currentHour) || currentMinute != 0) {
                return false
            }
        }

        // 检查是否在同一分钟内已经执行过
        if (lastExecuted != null) {
            val lastCalendar = Calendar.getInstance()
            lastCalendar.time = lastExecuted

            if (lastCalendar.get(Calendar.YEAR) == calendar.get(Calendar.YEAR) &&
                lastCalendar.get(Calendar.DAY_OF_YEAR) == calendar.get(Calendar.DAY_OF_YEAR) &&
                lastCalendar.get(Calendar.HOUR_OF_DAY) == currentHour &&
                lastCalendar.get(Calendar.MINUTE) == currentMinute) {
                return false // 同一分钟内已执行过
            }
        }

        return true
    }

    /**
     * 检查是否在时间范围内
     */
    private fun isWithinTimeRange(plan: MonitoringPlanEntity, currentTime: Date): Boolean {
        if (plan.startTime != null && currentTime.before(plan.startTime)) {
            return false
        }
        if (plan.endTime != null && currentTime.after(plan.endTime)) {
            return false
        }
        return true
    }

    /**
     * 检查是否在每日时间范围内
     */
    private fun isWithinDailyTimeRange(plan: MonitoringPlanEntity, currentTime: Date): Boolean {
        if (plan.dailyStartHour == -1 && plan.dailyEndHour == -1) {
            return true // 无限制
        }

        val calendar = Calendar.getInstance()
        calendar.time = currentTime
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)

        val startHour = if (plan.dailyStartHour == -1) 0 else plan.dailyStartHour
        val endHour = if (plan.dailyEndHour == -1) 23 else plan.dailyEndHour

        return currentHour in startHour..endHour
    }

    /**
     * 检查是否为有效工作日
     */
    private fun isValidWeekday(plan: MonitoringPlanEntity, currentTime: Date): Boolean {
        if (!plan.weekdaysOnly) {
            return true // 无限制
        }

        val calendar = Calendar.getInstance()
        calendar.time = currentTime
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)

        // 周一到周五 (Calendar.MONDAY = 2, Calendar.FRIDAY = 6)
        return dayOfWeek in Calendar.MONDAY..Calendar.FRIDAY
    }

    /**
     * 计算下次执行时间
     * @param plan 监控计划
     * @param currentTime 当前时间
     * @return 下次执行时间，如果无法计算则返回null
     */
    fun calculateNextExecutionTime(plan: MonitoringPlanEntity, currentTime: Date = Date()): Date? {
        return when (plan.operationType) {
            MonitoringOperationType.INTERVAL -> calculateNextIntervalTime(plan, currentTime)
            MonitoringOperationType.SCHEDULED -> calculateNextScheduledTime(plan, currentTime)
            MonitoringOperationType.MANUAL -> null // 手动执行无下次时间
        }
    }

    /**
     * 计算间隔监控的下次执行时间
     */
    private fun calculateNextIntervalTime(plan: MonitoringPlanEntity, currentTime: Date): Date {
        val lastExecuted = plan.lastExecutedAt ?: currentTime
        return Date(lastExecuted.time + plan.intervalSeconds * 1000L)
    }

    /**
     * 计算定时监控的下次执行时间
     */
    private fun calculateNextScheduledTime(plan: MonitoringPlanEntity, currentTime: Date): Date? {
        if (plan.scheduledConfig.isEmpty()) {
            return null
        }

        return try {
            val config = gson.fromJson(plan.scheduledConfig, ScheduledConfig::class.java)
            findNextScheduledTime(config, currentTime)
        } catch (e: JsonSyntaxException) {
            Log.e(TAG, "解析定时配置失败", e)
            null
        }
    }

    /**
     * 查找下一个定时执行时间
     */
    private fun findNextScheduledTime(config: ScheduledConfig, currentTime: Date): Date? {
        if (config.hours.isEmpty()) {
            return null
        }

        val calendar = Calendar.getInstance()
        calendar.time = currentTime

        // 尝试在今天找到下一个执行时间
        val nextTime = findNextTimeToday(config, calendar)
        if (nextTime != null) {
            return nextTime
        }

        // 如果今天没有，查找明天及以后的时间
        return findNextTimeFuture(config, calendar)
    }

    /**
     * 在今天查找下一个执行时间
     */
    private fun findNextTimeToday(config: ScheduledConfig, calendar: Calendar): Date? {
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = calendar.get(Calendar.MINUTE)

        val minutes = config.minutes.ifEmpty { listOf(0) }

        for (hour in config.hours.sorted()) {
            if (hour > currentHour) {
                calendar.set(Calendar.HOUR_OF_DAY, hour)
                calendar.set(Calendar.MINUTE, minutes.first())
                calendar.set(Calendar.SECOND, 0)
                calendar.set(Calendar.MILLISECOND, 0)
                return calendar.time
            } else if (hour == currentHour) {
                for (minute in minutes.sorted()) {
                    if (minute > currentMinute) {
                        calendar.set(Calendar.HOUR_OF_DAY, hour)
                        calendar.set(Calendar.MINUTE, minute)
                        calendar.set(Calendar.SECOND, 0)
                        calendar.set(Calendar.MILLISECOND, 0)
                        return calendar.time
                    }
                }
            }
        }

        return null
    }

    /**
     * 查找未来的执行时间
     */
    private fun findNextTimeFuture(config: ScheduledConfig, calendar: Calendar): Date? {
        // 设置为明天的第一个执行时间
        calendar.add(Calendar.DAY_OF_YEAR, 1)
        calendar.set(Calendar.HOUR_OF_DAY, config.hours.minOrNull() ?: 0)
        calendar.set(Calendar.MINUTE, if (config.minutes.isNotEmpty()) config.minutes.minOrNull() ?: 0 else 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)

        // 如果有星期限制，需要找到下一个有效的星期
        if (config.weekdays.isNotEmpty()) {
            var attempts = 0
            while (attempts < 7) { // 最多尝试7天
                val weekday = calendar.get(Calendar.DAY_OF_WEEK)
                val convertedWeekday = if (weekday == Calendar.SUNDAY) 7 else weekday - 1

                if (config.weekdays.contains(convertedWeekday)) {
                    return calendar.time
                }

                calendar.add(Calendar.DAY_OF_YEAR, 1)
                attempts++
            }
        }

        return calendar.time
    }

    /**
     * 创建简单的间隔监控配置
     * @param intervalSeconds 间隔秒数
     * @return 监控计划配置
     */
    fun createIntervalConfig(intervalSeconds: Int): Map<String, Any> {
        return mapOf(
            "operationType" to MonitoringOperationType.INTERVAL,
            "intervalSeconds" to intervalSeconds
        )
    }

    /**
     * 创建定时监控配置
     * @param hours 小时列表
     * @param minutes 分钟列表
     * @param weekdays 星期列表（可选）
     * @return 监控计划配置
     */
    fun createScheduledConfig(
        hours: List<Int>,
        minutes: List<Int> = listOf(0),
        weekdays: List<Int> = emptyList()
    ): Map<String, Any> {
        val scheduledConfig = ScheduledConfig(
            hours = hours,
            minutes = minutes,
            weekdays = weekdays
        )

        return mapOf(
            "operationType" to MonitoringOperationType.SCHEDULED,
            "scheduledConfig" to gson.toJson(scheduledConfig)
        )
    }

    /**
     * 验证定时配置的有效性
     * @param scheduledConfig 定时配置JSON字符串
     * @return 是否有效
     */
    fun validateScheduledConfig(scheduledConfig: String): Boolean {
        if (scheduledConfig.isEmpty()) {
            return false
        }

        return try {
            val config = gson.fromJson(scheduledConfig, ScheduledConfig::class.java)

            // 验证小时范围
            config.hours.all { it in 0..23 } &&
            // 验证分钟范围
            config.minutes.all { it in 0..59 } &&
            // 验证星期范围
            config.weekdays.all { it in 1..7 } &&
            // 至少要有小时配置
            config.hours.isNotEmpty()
        } catch (e: Exception) {
            false
        }
    }
}