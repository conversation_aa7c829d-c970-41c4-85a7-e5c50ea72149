package dev.pigmomo.yhkit2025.data.utils

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dev.pigmomo.yhkit2025.data.model.ProductChangeType
import dev.pigmomo.yhkit2025.data.model.MonitoringOperationType

/**
 * 商品相关类型转换器
 * 用于在Room数据库中存储和检索复杂类型
 */
class ProductTypeConverter {
    private val gson = Gson()
    
    /**
     * 将ProductChangeType枚举转换为字符串
     */
    @TypeConverter
    fun fromProductChangeType(changeType: ProductChangeType?): String? {
        return changeType?.name
    }
    
    /**
     * 将字符串转换为ProductChangeType枚举
     */
    @TypeConverter
    fun toProductChangeType(changeType: String?): ProductChangeType? {
        return changeType?.let {
            try {
                ProductChangeType.valueOf(it)
            } catch (e: IllegalArgumentException) {
                null
            }
        }
    }

    /**
     * 将MonitoringOperationType枚举转换为字符串
     */
    @TypeConverter
    fun fromMonitoringOperationType(operationType: MonitoringOperationType?): String? {
        return operationType?.name
    }

    /**
     * 将字符串转换为MonitoringOperationType枚举
     */
    @TypeConverter
    fun toMonitoringOperationType(operationType: String?): MonitoringOperationType? {
        return operationType?.let {
            try {
                MonitoringOperationType.valueOf(it)
            } catch (e: IllegalArgumentException) {
                MonitoringOperationType.INTERVAL // 默认值
            }
        }
    }
    
    /**
     * 将Map<String, Any>转换为JSON字符串
     */
    @TypeConverter
    fun fromMap(value: Map<String, Any>?): String {
        return if (value == null || value.isEmpty()) {
            "{}"
        } else {
            gson.toJson(value)
        }
    }
    
    /**
     * 将JSON字符串转换为Map<String, Any>
     */
    @TypeConverter
    fun toMap(value: String?): Map<String, Any> {
        return if (value.isNullOrBlank() || value == "{}") {
            emptyMap()
        } else {
            try {
                val type = object : TypeToken<Map<String, Any>>() {}.type
                gson.fromJson(value, type)
            } catch (e: Exception) {
                emptyMap()
            }
        }
    }
}
