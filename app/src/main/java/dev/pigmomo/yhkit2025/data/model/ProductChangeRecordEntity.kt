package dev.pigmomo.yhkit2025.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import androidx.room.ColumnInfo
import androidx.room.ForeignKey
import androidx.room.Index
import dev.pigmomo.yhkit2025.data.utils.DateTypeConverter
import java.util.Date

/**
 * 商品变化类型枚举
 */
enum class ProductChangeType {
    /**
     * 价格变化
     */
    PRICE_CHANGE,
    
    /**
     * 库存变化
     */
    STOCK_CHANGE,
    
    /**
     * 可用性变化
     */
    AVAILABILITY_CHANGE,
    
    /**
     * 秒杀状态变化
     */
    SECKILL_STATUS_CHANGE,
    
    /**
     * 限购信息变化
     */
    RESTRICT_CHANGE,
    
    /**
     * 商品信息变化
     */
    INFO_CHANGE,
    
    /**
     * 其他变化
     */
    OTHER_CHANGE
}

/**
 * 商品变化记录实体类
 * 用于记录商品信息的历史变化
 */
@Entity(
    tableName = "product_change_records",
    foreignKeys = [
        ForeignKey(
            entity = ProductMonitorEntity::class,
            parentColumns = ["id"],
            childColumns = ["product_id"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [
        Index("product_id"),
        Index("change_time"),
        Index("change_type")
    ]
)
@TypeConverters(DateTypeConverter::class)
data class ProductChangeRecordEntity(
    /**
     * 变化记录ID - 主键
     */
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    
    /**
     * 关联的商品ID
     */
    @ColumnInfo(name = "product_id")
    val productId: String,
    
    /**
     * 变化类型
     */
    @ColumnInfo(name = "change_type")
    val changeType: ProductChangeType,
    
    /**
     * 变化时间
     */
    @ColumnInfo(name = "change_time")
    val changeTime: Date = Date(),
    
    /**
     * 变化字段名称
     */
    @ColumnInfo(name = "field_name")
    val fieldName: String,
    
    /**
     * 变化前的值
     */
    @ColumnInfo(name = "old_value")
    val oldValue: String = "",
    
    /**
     * 变化后的值
     */
    @ColumnInfo(name = "new_value")
    val newValue: String = "",
    
    /**
     * 变化描述
     */
    @ColumnInfo(name = "change_description")
    val changeDescription: String = "",
    
    /**
     * 变化幅度（用于数值类型的变化）
     */
    @ColumnInfo(name = "change_amount")
    val changeAmount: Double = 0.0,
    
    /**
     * 变化百分比（用于价格等变化）
     */
    @ColumnInfo(name = "change_percentage")
    val changePercentage: Double = 0.0,
    
    /**
     * 是否为重要变化
     */
    @ColumnInfo(name = "is_important")
    val isImportant: Boolean = false,
    
    /**
     * 额外数据（JSON格式）
     */
    @ColumnInfo(name = "extra_data")
    val extraData: String = ""
)
