package dev.pigmomo.yhkit2025.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import dev.pigmomo.yhkit2025.data.model.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.MonitoringType
import dev.pigmomo.yhkit2025.data.model.MonitoringOperationType
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 监控计划数据访问对象接口
 * 提供对监控计划数据的CRUD操作
 */
@Dao
interface MonitoringPlanDao {
    /**
     * 插入新的监控计划
     * 
     * @param plan 要插入的监控计划实体
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(plan: MonitoringPlanEntity): Long
    
    /**
     * 批量插入监控计划
     * 
     * @param plans 要插入的监控计划实体列表
     * @return 插入的行ID列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(plans: List<MonitoringPlanEntity>): List<Long>
    
    /**
     * 更新监控计划
     * 
     * @param plan 要更新的监控计划实体
     * @return 更新的行数
     */
    @Update
    suspend fun update(plan: MonitoringPlanEntity): Int
    
    /**
     * 删除监控计划
     * 
     * @param plan 要删除的监控计划实体
     * @return 删除的行数
     */
    @Delete
    suspend fun delete(plan: MonitoringPlanEntity): Int
    
    /**
     * 根据ID删除监控计划
     * 
     * @param planId 要删除的监控计划ID
     * @return 删除的行数
     */
    @Query("DELETE FROM monitoring_plans WHERE id = :planId")
    suspend fun deleteById(planId: Int): Int
    
    /**
     * 删除指定账户的所有监控计划
     * 
     * @param accountUid 账户UID
     * @return 删除的行数
     */
    @Query("DELETE FROM monitoring_plans WHERE account_uid = :accountUid")
    suspend fun deleteByAccountUid(accountUid: String): Int
    
    /**
     * 获取所有监控计划
     * 
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans ORDER BY createdAt DESC")
    fun getAllPlans(): Flow<List<MonitoringPlanEntity>>
    
    /**
     * 获取所有已启用的监控计划
     * 
     * @return 已启用的监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE isEnabled = 1 ORDER BY createdAt DESC")
    fun getEnabledPlans(): Flow<List<MonitoringPlanEntity>>
    
    /**
     * 根据ID获取监控计划
     * 
     * @param planId 监控计划ID
     * @return 监控计划实体
     */
    @Query("SELECT * FROM monitoring_plans WHERE id = :planId LIMIT 1")
    suspend fun getPlanById(planId: Int): MonitoringPlanEntity?
    
    /**
     * 根据账户UID获取监控计划
     * 
     * @param accountUid 账户UID
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE account_uid = :accountUid ORDER BY createdAt DESC")
    fun getPlansByAccountUid(accountUid: String): Flow<List<MonitoringPlanEntity>>
    
    /**
     * 根据账户手机号获取监控计划
     * 
     * @param phoneNumber 账户手机号
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE account_phoneNumber = :phoneNumber ORDER BY createdAt DESC")
    fun getPlansByAccountPhone(phoneNumber: String): Flow<List<MonitoringPlanEntity>>
    
    /**
     * 根据监控类型获取监控计划
     * 
     * @param type 监控类型
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE type = :type ORDER BY createdAt DESC")
    fun getPlansByType(type: MonitoringType): Flow<List<MonitoringPlanEntity>>
    
    /**
     * 根据商品ID获取包含该商品的监控计划
     * 由于使用了JSON存储，需要使用LIKE查询
     * 
     * @param productId 商品ID
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE productIds LIKE '%' || :productId || '%' ORDER BY createdAt DESC")
    fun getPlansByProductId(productId: String): Flow<List<MonitoringPlanEntity>>
    
    /**
     * 更新监控计划的最后执行时间
     * 
     * @param planId 监控计划ID
     * @param lastExecutedAt 最后执行时间
     * @return 更新的行数
     */
    @Query("UPDATE monitoring_plans SET lastExecutedAt = :lastExecutedAt WHERE id = :planId")
    suspend fun updateLastExecutedTime(planId: Int, lastExecutedAt: Date): Int
    
    /**
     * 更新监控计划的启用状态
     * 
     * @param planId 监控计划ID
     * @param isEnabled 是否启用
     * @return 更新的行数
     */
    @Query("UPDATE monitoring_plans SET isEnabled = :isEnabled WHERE id = :planId")
    suspend fun updateEnabledStatus(planId: Int, isEnabled: Boolean): Int
    
    /**
     * 获取需要执行的监控计划
     * 筛选条件：已启用且上次执行时间为空或已超过间隔时间
     * 
     * @param currentTime 当前时间
     * @return 需要执行的监控计划列表
     */
    @Transaction
    @Query("""
        SELECT * FROM monitoring_plans 
        WHERE isEnabled = 1 AND (
            lastExecutedAt IS NULL OR 
            (strftime('%s', :currentTime) - strftime('%s', lastExecutedAt)) > intervalSeconds
        )
        ORDER BY lastExecutedAt ASC
    """)
    suspend fun getPlansToExecute(currentTime: Date): List<MonitoringPlanEntity>
    
    /**
     * 获取监控计划总数
     *
     * @return 监控计划总数
     */
    @Query("SELECT COUNT(*) FROM monitoring_plans")
    suspend fun getCount(): Int

    /**
     * 根据操作类型获取监控计划
     *
     * @param operationType 监控操作类型
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE operationType = :operationType ORDER BY priority DESC, createdAt DESC")
    fun getPlansByOperationType(operationType: MonitoringOperationType): Flow<List<MonitoringPlanEntity>>

    /**
     * 获取间隔监控计划（需要执行的）
     * 筛选条件：操作类型为INTERVAL且已启用且上次执行时间为空或已超过间隔时间
     *
     * @param currentTime 当前时间
     * @return 需要执行的间隔监控计划列表
     */
    @Transaction
    @Query("""
        SELECT * FROM monitoring_plans
        WHERE isEnabled = 1
        AND operationType = 'INTERVAL'
        AND (
            lastExecutedAt IS NULL OR
            (strftime('%s', :currentTime) - strftime('%s', lastExecutedAt)) >= intervalSeconds
        )
        AND (startTime IS NULL OR :currentTime >= startTime)
        AND (endTime IS NULL OR :currentTime <= endTime)
        AND (maxExecutions = -1 OR executedCount < maxExecutions)
        ORDER BY priority DESC, lastExecutedAt ASC
    """)
    suspend fun getIntervalPlansToExecute(currentTime: Date): List<MonitoringPlanEntity>

    /**
     * 获取定时监控计划（需要执行的）
     * 筛选条件：操作类型为SCHEDULED且已启用
     *
     * @param currentTime 当前时间
     * @return 需要执行的定时监控计划列表
     */
    @Transaction
    @Query("""
        SELECT * FROM monitoring_plans
        WHERE isEnabled = 1
        AND operationType = 'SCHEDULED'
        AND (startTime IS NULL OR :currentTime >= startTime)
        AND (endTime IS NULL OR :currentTime <= endTime)
        AND (maxExecutions = -1 OR executedCount < maxExecutions)
        ORDER BY priority DESC, createdAt DESC
    """)
    suspend fun getScheduledPlansToExecute(currentTime: Date): List<MonitoringPlanEntity>

    /**
     * 更新监控计划的执行次数
     *
     * @param planId 监控计划ID
     * @param executedCount 新的执行次数
     */
    @Query("UPDATE monitoring_plans SET executedCount = :executedCount WHERE id = :planId")
    suspend fun updateExecutedCount(planId: Int, executedCount: Int)

    /**
     * 增加监控计划的执行次数
     *
     * @param planId 监控计划ID
     */
    @Query("UPDATE monitoring_plans SET executedCount = executedCount + 1 WHERE id = :planId")
    suspend fun incrementExecutedCount(planId: Int)

    /**
     * 更新监控计划的最后执行时间
     *
     * @param planId 监控计划ID
     * @param executedTime 执行时间，默认为当前时间
     */
    @Query("UPDATE monitoring_plans SET lastExecutedAt = :executedTime WHERE id = :planId")
    suspend fun updateLastExecutedAt(planId: Int, executedTime: Date = Date())

    /**
     * 更新监控计划的操作类型
     *
     * @param planId 监控计划ID
     * @param operationType 新的操作类型
     */
    @Query("UPDATE monitoring_plans SET operationType = :operationType WHERE id = :planId")
    suspend fun updateOperationType(planId: Int, operationType: MonitoringOperationType)

    /**
     * 更新监控计划的间隔时间
     *
     * @param planId 监控计划ID
     * @param intervalSeconds 新的间隔时间（秒）
     */
    @Query("UPDATE monitoring_plans SET intervalSeconds = :intervalSeconds WHERE id = :planId")
    suspend fun updateIntervalSeconds(planId: Int, intervalSeconds: Int)

    /**
     * 更新监控计划的定时配置
     *
     * @param planId 监控计划ID
     * @param scheduledConfig 新的定时配置
     */
    @Query("UPDATE monitoring_plans SET scheduledConfig = :scheduledConfig WHERE id = :planId")
    suspend fun updateScheduledConfig(planId: Int, scheduledConfig: String)

    /**
     * 更新监控计划的优先级
     *
     * @param planId 监控计划ID
     * @param priority 新的优先级
     */
    @Query("UPDATE monitoring_plans SET priority = :priority WHERE id = :planId")
    suspend fun updatePriority(planId: Int, priority: Int)

    /**
     * 获取高优先级的监控计划
     *
     * @param minPriority 最小优先级
     * @return 高优先级监控计划列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE priority >= :minPriority ORDER BY priority DESC, createdAt DESC")
    fun getHighPriorityPlans(minPriority: Int = 7): Flow<List<MonitoringPlanEntity>>

    /**
     * 获取已达到最大执行次数的监控计划
     *
     * @return 已完成的监控计划列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE maxExecutions != -1 AND executedCount >= maxExecutions ORDER BY createdAt DESC")
    fun getCompletedPlans(): Flow<List<MonitoringPlanEntity>>

    /**
     * 重置监控计划的执行次数
     *
     * @param planId 监控计划ID
     */
    @Query("UPDATE monitoring_plans SET executedCount = 0 WHERE id = :planId")
    suspend fun resetExecutedCount(planId: Int)

    /**
     * 根据服务类型获取监控计划
     *
     * @param serviceType 服务类型
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE serviceType = :serviceType ORDER BY createdAt DESC")
    fun getPlansByServiceType(serviceType: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据店铺ID获取监控计划
     *
     * @param shopId 店铺ID
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE shopId = :shopId ORDER BY createdAt DESC")
    fun getPlansByShopId(shopId: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据卖家ID获取监控计划
     *
     * @param sellerId 卖家ID
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE sellerId = :sellerId ORDER BY createdAt DESC")
    fun getPlansBySellerId(sellerId: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据城市ID获取监控计划
     *
     * @param cityId 城市ID
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE cityId = :cityId ORDER BY createdAt DESC")
    fun getPlansByCityId(cityId: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 根据地址ID获取监控计划
     *
     * @param addressId 地址ID
     * @return 监控计划实体列表的Flow
     */
    @Query("SELECT * FROM monitoring_plans WHERE addressId = :addressId ORDER BY createdAt DESC")
    fun getPlansByAddressId(addressId: String): Flow<List<MonitoringPlanEntity>>

    /**
     * 更新监控计划的服务类型
     *
     * @param planId 监控计划ID
     * @param serviceType 新的服务类型
     */
    @Query("UPDATE monitoring_plans SET serviceType = :serviceType WHERE id = :planId")
    suspend fun updateServiceType(planId: Int, serviceType: String)

    /**
     * 更新监控计划的地址ID
     *
     * @param planId 监控计划ID
     * @param addressId 新的地址ID
     */
    @Query("UPDATE monitoring_plans SET addressId = :addressId WHERE id = :planId")
    suspend fun updateAddressId(planId: Int, addressId: String)

    /**
     * 更新监控计划的XYH业务参数
     *
     * @param planId 监控计划ID
     * @param xyhBizParams 新的XYH业务参数
     */
    @Query("UPDATE monitoring_plans SET xyhBizParams = :xyhBizParams WHERE id = :planId")
    suspend fun updateXyhBizParams(planId: Int, xyhBizParams: String)

    /**
     * 更新监控计划的Web XYH业务参数
     *
     * @param planId 监控计划ID
     * @param webXyhBizParams 新的Web XYH业务参数
     */
    @Query("UPDATE monitoring_plans SET webXyhBizParams = :webXyhBizParams WHERE id = :planId")
    suspend fun updateWebXyhBizParams(planId: Int, webXyhBizParams: String)

    /**
     * 更新监控计划的店铺ID
     *
     * @param planId 监控计划ID
     * @param shopId 新的店铺ID
     */
    @Query("UPDATE monitoring_plans SET shopId = :shopId WHERE id = :planId")
    suspend fun updateShopId(planId: Int, shopId: String)

    /**
     * 更新监控计划的卖家ID
     *
     * @param planId 监控计划ID
     * @param sellerId 新的卖家ID
     */
    @Query("UPDATE monitoring_plans SET sellerId = :sellerId WHERE id = :planId")
    suspend fun updateSellerId(planId: Int, sellerId: String)

    /**
     * 更新监控计划的城市ID
     *
     * @param planId 监控计划ID
     * @param cityId 新的城市ID
     */
    @Query("UPDATE monitoring_plans SET cityId = :cityId WHERE id = :planId")
    suspend fun updateCityId(planId: Int, cityId: String)

    /**
     * 更新监控计划的区域
     *
     * @param planId 监控计划ID
     * @param district 新的区域
     */
    @Query("UPDATE monitoring_plans SET district = :district WHERE id = :planId")
    suspend fun updateDistrict(planId: Int, district: String)

    /**
     * 获取具有相同RequestService配置的监控计划
     *
     * @param serviceType 服务类型
     * @param accountUid 账户UID
     * @param shopId 店铺ID
     * @param sellerId 卖家ID
     * @return 监控计划实体列表的Flow
     */
    @Query("""
        SELECT * FROM monitoring_plans
        WHERE serviceType = :serviceType
        AND account_uid = :accountUid
        AND shopId = :shopId
        AND sellerId = :sellerId
        ORDER BY createdAt DESC
    """)
    fun getPlansWithSimilarConfig(
        serviceType: String,
        accountUid: String,
        shopId: String,
        sellerId: String
    ): Flow<List<MonitoringPlanEntity>>
} 