package dev.pigmomo.yhkit2025.utils.productmonitor

import android.annotation.SuppressLint
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.api.model.product.ProductDetailData
import dev.pigmomo.yhkit2025.data.model.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.ProductChangeType
import dev.pigmomo.yhkit2025.data.model.ProductMonitorEntity
import java.util.Date
import kotlin.math.abs

/**
 * 商品监控工具类
 * 提供商品数据转换和变化检测功能
 */
object ProductMonitorUtils {

    /**
     * 将Product转换为ProductMonitorEntity
     * @param product 原始商品数据
     * @param isFirstTime 是否为首次添加
     * @return ProductMonitorEntity
     */
    fun convertToProductMonitorEntity(
        product: Product,
        isFirstTime: Boolean = false
    ): ProductMonitorEntity {
        val currentTime = Date()

        return ProductMonitorEntity(
            id = product.id,
            originalSkuCode = product.originalskucode,
            title = product.title,
            subtitle = product.subtitle,
            imgUrl = product.imgurl,
            currentPrice = product.price?.value ?: 0,
            marketPrice = product.price?.market ?: 0,
            priceKind = product.price?.priceKind ?: "",
            stockNum = product.stocknum,
            available = product.available,
            isSeckill = product.isseckill,
            canNotBuy = product.canNotBuy,
            sellerId = product.sellerid,
            categoryId = product.categoryId,
            productTags = product.producttags,
            specDesc = product.spec?.desc ?: "",
            restrictLimit = product.restrict?.limit ?: 0,
            restrictType = product.restrict?.restricttype ?: 0,
            fewStockRemark = product.fewstockremark,
            deliveryTimeDesc = product.deliverytimedesc,
            isPerformanceHourHour = product.isperformancehourhour,
            grossWeight = product.grossWeight,
            expirationDate = product.expirationDate,
            isBulkItem = product.isbulkitem,
            goodsTag = product.goodstag,
            goodsTagId = product.goodstagid,
            bundlePromoCode = product.bundlepromocode,
            orderRemark = product.orderremark,
            firstAddTime = if (isFirstTime) currentTime else Date(product.addTime),
            lastUpdateTime = currentTime,
            isMonitoringEnabled = true,
            monitorNote = ""
        )
    }

    /**
     * 将ProductDetailData转换为ProductMonitorEntity
     * @param productDetail 商品详情数据
     * @param isFirstTime 是否为首次添加
     * @return ProductMonitorEntity
     */
    fun convertProductDetailToMonitorEntity(
        productDetail: ProductDetailData,
        isFirstTime: Boolean = false
    ): ProductMonitorEntity {
        val currentTime = Date()

        // 提取主图片URL
        val mainImageUrl = productDetail.mainimgs?.firstOrNull()?.imgurl
            ?: productDetail.smallImg
            ?: ""

        // 提取商品标签
        val productTags = mutableListOf<String>()
        productDetail.titletaglist?.forEach { titleTag ->
            titleTag.type?.let { productTags.add(it) }
        }
        productDetail.promotion?.taglist?.forEach { tag ->
            tag.text?.let { productTags.add(it) }
        }

        // 提取规格描述
        val specDesc = buildString {
            productDetail.spec?.forEach { spec ->
                spec.pid?.let { append("$it ") }
            }
            productDetail.place?.forEach { place ->
                place.value?.let { append("产地:$it ") }
            }
        }.trim()

        // 提取限购信息（从restricts字符串列表中解析）
        var restrictLimit = 0
        var restrictType = 0
        productDetail.restricts?.firstOrNull()?.let { restrictStr ->
            // 这里可能需要根据实际的restrict格式进行解析
            // 假设格式为 "limit:5,type:1" 或类似格式
            try {
                val parts = restrictStr.split(",")
                parts.forEach { part ->
                    val keyValue = part.split(":")
                    if (keyValue.size == 2) {
                        when (keyValue[0].trim()) {
                            "limit" -> restrictLimit = keyValue[1].trim().toIntOrNull() ?: 0
                            "type" -> restrictType = keyValue[1].trim().toIntOrNull() ?: 0
                        }
                    }
                }
            } catch (e: Exception) {
                // 解析失败时使用默认值
            }
        }

        // 判断是否可购买
        val canNotBuy = productDetail.price?.canBuy != 1

        // 判断是否为秒杀商品（根据价格类型判断）
        val isSeckill = when (productDetail.price?.pricetype?.lowercase()) {
            "seckill", "秒杀" -> 1
            else -> 0
        }

        // 提取配送时间描述
        val deliveryTimeDesc = productDetail.skuservicedes?.servicename ?: ""

        return ProductMonitorEntity(
            id = productDetail.id ?: "",
            originalSkuCode = productDetail.batchskucode ?: "",
            title = productDetail.title ?: "",
            subtitle = productDetail.subtitle ?: "",
            imgUrl = mainImageUrl,
            currentPrice = productDetail.price?.value ?: 0,
            marketPrice = productDetail.price?.market ?: 0,
            priceKind = productDetail.price?.pricetype ?: "",
            stockNum = productDetail.stock?.count ?: 0,
            available = if (productDetail.status == 1) 1 else 0,
            isSeckill = isSeckill,
            canNotBuy = canNotBuy,
            sellerId = productDetail.seller?.id ?: "",
            categoryId = productDetail.sapcategoryid ?: "",
            productTags = productTags,
            specDesc = specDesc,
            restrictLimit = restrictLimit,
            restrictType = restrictType,
            fewStockRemark = productDetail.stock?.desc ?: "",
            deliveryTimeDesc = deliveryTimeDesc,
            isPerformanceHourHour = false, // 商品详情页中没有直接的小时达标识
            grossWeight = 0.0, // 商品详情页中没有毛重信息
            expirationDate = productDetail.expiration ?: 0,
            isBulkItem = if (productDetail.batchflag == 1) 1 else 0,
            goodsTag = "",
            goodsTagId = 0,
            bundlePromoCode = "",
            orderRemark = "",
            firstAddTime = if (isFirstTime) currentTime else currentTime,
            lastUpdateTime = currentTime,
            isMonitoringEnabled = true,
            monitorNote = ""
        )
    }

    /**
     * 检测商品变化并生成变化记录
     * @param oldProduct 旧的商品数据
     * @param newProduct 新的商品数据
     * @return 变化记录列表
     */
    fun detectProductChanges(
        oldProduct: ProductMonitorEntity,
        newProduct: ProductMonitorEntity
    ): List<ProductChangeRecordEntity> {
        val changes = mutableListOf<ProductChangeRecordEntity>()
        val changeTime = Date()

        // 检测价格变化
        if (oldProduct.currentPrice != newProduct.currentPrice) {
            val changeAmount = newProduct.currentPrice - oldProduct.currentPrice
            val changePercentage = if (oldProduct.currentPrice > 0) {
                (changeAmount.toDouble() / oldProduct.currentPrice) * 100
            } else 0.0

            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.PRICE_CHANGE,
                    changeTime = changeTime,
                    fieldName = "currentPrice",
                    oldValue = oldProduct.currentPrice.toString(),
                    newValue = newProduct.currentPrice.toString(),
                    changeDescription = "价格从 ${formatPrice(oldProduct.currentPrice)} 变为 ${
                        formatPrice(
                            newProduct.currentPrice
                        )
                    }",
                    changeAmount = changeAmount.toDouble(),
                    changePercentage = changePercentage,
                    isImportant = abs(changePercentage) >= 10.0 // 价格变化超过10%认为是重要变化
                )
            )
        }

        // 检测市场价格变化
        if (oldProduct.marketPrice != newProduct.marketPrice) {
            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.PRICE_CHANGE,
                    changeTime = changeTime,
                    fieldName = "marketPrice",
                    oldValue = oldProduct.marketPrice.toString(),
                    newValue = newProduct.marketPrice.toString(),
                    changeDescription = "市场价从 ${formatPrice(oldProduct.marketPrice)} 变为 ${
                        formatPrice(
                            newProduct.marketPrice
                        )
                    }",
                    changeAmount = (newProduct.marketPrice - oldProduct.marketPrice).toDouble(),
                    isImportant = false
                )
            )
        }

        // 检测库存变化
        if (oldProduct.stockNum != newProduct.stockNum) {
            val changeAmount = newProduct.stockNum - oldProduct.stockNum
            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.STOCK_CHANGE,
                    changeTime = changeTime,
                    fieldName = "stockNum",
                    oldValue = oldProduct.stockNum.toString(),
                    newValue = newProduct.stockNum.toString(),
                    changeDescription = "库存从 ${oldProduct.stockNum} 变为 ${newProduct.stockNum}",
                    changeAmount = changeAmount.toDouble(),
                    isImportant = (oldProduct.stockNum <= 0 && newProduct.stockNum > 0) ||
                            (oldProduct.stockNum > 0 && newProduct.stockNum <= 0) // 缺货状态变化
                )
            )
        }

        // 检测可用性变化
        if (oldProduct.available != newProduct.available) {
            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.AVAILABILITY_CHANGE,
                    changeTime = changeTime,
                    fieldName = "available",
                    oldValue = oldProduct.available.toString(),
                    newValue = newProduct.available.toString(),
                    changeDescription = "可用性从 ${if (oldProduct.available == 1) "可用" else "不可用"} 变为 ${if (newProduct.available == 1) "可用" else "不可用"}",
                    isImportant = true // 可用性变化总是重要的
                )
            )
        }

        // 检测秒杀状态变化
        if (oldProduct.isSeckill != newProduct.isSeckill) {
            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.SECKILL_STATUS_CHANGE,
                    changeTime = changeTime,
                    fieldName = "isSeckill",
                    oldValue = oldProduct.isSeckill.toString(),
                    newValue = newProduct.isSeckill.toString(),
                    changeDescription = "秒杀状态从 ${if (oldProduct.isSeckill == 1) "是" else "否"} 变为 ${if (newProduct.isSeckill == 1) "是" else "否"}",
                    isImportant = true // 秒杀状态变化总是重要的
                )
            )
        }

        // 检测限购信息变化
        if (oldProduct.restrictLimit != newProduct.restrictLimit || oldProduct.restrictType != newProduct.restrictType) {
            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.RESTRICT_CHANGE,
                    changeTime = changeTime,
                    fieldName = "restrict",
                    oldValue = "${oldProduct.restrictLimit}:${oldProduct.restrictType}",
                    newValue = "${newProduct.restrictLimit}:${newProduct.restrictType}",
                    changeDescription = "限购信息变化：限购数量 ${oldProduct.restrictLimit} -> ${newProduct.restrictLimit}，限购类型 ${oldProduct.restrictType} -> ${newProduct.restrictType}",
                    isImportant = false
                )
            )
        }

        // 检测标题变化
        if (oldProduct.title != newProduct.title) {
            changes.add(
                ProductChangeRecordEntity(
                    productId = newProduct.id,
                    changeType = ProductChangeType.INFO_CHANGE,
                    changeTime = changeTime,
                    fieldName = "title",
                    oldValue = oldProduct.title,
                    newValue = newProduct.title,
                    changeDescription = "标题变化",
                    isImportant = false
                )
            )
        }

        return changes
    }

    /**
     * 格式化价格显示
     * @param priceInCents 价格（分）
     * @return 格式化的价格字符串
     */
    @SuppressLint("DefaultLocale")
    private fun formatPrice(priceInCents: Int): String {
        return "¥${String.format("%.2f", priceInCents / 100.0)}"
    }

    /**
     * 判断商品是否有重要变化
     * @param changes 变化记录列表
     * @return 是否有重要变化
     */
    fun hasImportantChanges(changes: List<ProductChangeRecordEntity>): Boolean {
        return changes.any { it.isImportant }
    }

    /**
     * 获取变化摘要
     * @param changes 变化记录列表
     * @return 变化摘要字符串
     */
    fun getChangesSummary(changes: List<ProductChangeRecordEntity>): String {
        if (changes.isEmpty()) return "无变化"

        val summary = mutableListOf<String>()

        changes.groupBy { it.changeType }.forEach { (type, typeChanges) ->
            when (type) {
                ProductChangeType.PRICE_CHANGE -> {
                    val priceChanges = typeChanges.filter { it.fieldName == "currentPrice" }
                    if (priceChanges.isNotEmpty()) {
                        summary.add("价格变化${priceChanges.size}次")
                    }
                }
                ProductChangeType.STOCK_CHANGE -> summary.add("库存变化${typeChanges.size}次")
                ProductChangeType.AVAILABILITY_CHANGE -> summary.add("可用性变化${typeChanges.size}次")
                ProductChangeType.SECKILL_STATUS_CHANGE -> summary.add("秒杀状态变化${typeChanges.size}次")
                ProductChangeType.RESTRICT_CHANGE -> summary.add("限购信息变化${typeChanges.size}次")
                ProductChangeType.INFO_CHANGE -> summary.add("商品信息变化${typeChanges.size}次")
                ProductChangeType.OTHER_CHANGE -> summary.add("其他变化${typeChanges.size}次")
            }
        }

        return summary.joinToString(", ")
    }
}