package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.api.model.cart.CartItem
import dev.pigmomo.yhkit2025.api.model.cart.CartModelTypes
import dev.pigmomo.yhkit2025.api.model.cart.CartModelWrapper
import dev.pigmomo.yhkit2025.api.model.cart.Product
import dev.pigmomo.yhkit2025.ui.components.CartModelItem
import dev.pigmomo.yhkit2025.ui.components.TabBar
import dev.pigmomo.yhkit2025.ui.components.TabItem
import dev.pigmomo.yhkit2025.ui.components.TokenActionButton
import dev.pigmomo.yhkit2025.data.model.OrderTokenEntity
import dev.pigmomo.yhkit2025.service.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import dev.pigmomo.yhkit2025.ui.theme.cardThemeOverlay
import kotlinx.coroutines.launch
import java.text.DecimalFormat

/**
 * 购物车对话框组件
 *
 * @param cartItems 购物车项目列表
 * @param onDismiss 对话框关闭回调
 * @param onItemClick 商品项点击回调
 * @param onAddItem 增加商品数量回调
 * @param onReduceItem 减少商品数量回调
 * @param onDeleteItem 删除商品回调
 * @param onItemLongClick 商品项长按回调，用于显示删除对话框
 * @param onSaveProductConfig 保存商品配置回调
 * @param onCartItemSelect 选中购物车项回调
 * @param onProductSearch 商品搜索回调
 * @param onRefresh 刷新购物车回调
 */
@Composable
fun CartDialog(
    cartItems: List<CartItem>,
    currentCartItem: CartItem?,
    onDismiss: () -> Unit = {},
    onItemClick: (Product) -> Unit = {},
    onAddItem: (Product) -> Unit = {},
    onReduceItem: (Product) -> Unit = {},
    onDeleteItem: (Product) -> Unit = {},
    onItemLongClick: (Product) -> Unit = onDeleteItem,
    onSaveProductConfig: (String) -> Unit = {},
    onCartItemSelect: (CartItem?) -> Unit = {},
    onProductSearch: () -> Unit = {},
    onRefresh: () -> Unit = {}
) {
    // 购物车类型标签
    val cartTypes = remember(cartItems) {
        val types = mutableListOf<String>()
        cartItems.forEach { item ->
            when (item.type) {
                "normal" -> if ("normal" !in types) {
                    types.add("正常")
                }

                "presale" -> if ("presale" !in types) {
                    types.add("预售")
                }
            }
        }
        types
    }

    var selectedTabIndex by remember {
        mutableIntStateOf(
            if (currentCartItem == null) {
                0
            } else {
                val index = cartItems.indexOf(currentCartItem)
                if (index >= 0) index else 0
            }
        )
    }

    // 监控任务弹窗状态
    var showMonitoringDialog by remember { mutableStateOf(false) }

    // 添加监控计划弹窗状态
    var showAddMonitoringDialog by remember { mutableStateOf(false) }
    var availableAccounts by remember { mutableStateOf<List<OrderTokenEntity>>(emptyList()) }

    // 获取上下文和协程作用域
    val context = LocalContext.current
    val scope = rememberCoroutineScope()

    // 获取服务实例
    val monitoringPlanRepository = remember { MonitoringServiceManager.getMonitoringPlanRepository(context) }
    val database = remember { dev.pigmomo.yhkit2025.data.database.AppDatabase.getDatabase(context) }
    val orderTokenDao = remember { database.orderTokenDao() }

    // 加载可用账户
    LaunchedEffect(Unit) {
        scope.launch {
            try {
                orderTokenDao.getAllOrderTokens().collect { accounts ->
                    availableAccounts = accounts
                }
            } catch (e: Exception) {
                // 处理错误
            }
        }
    }

    // 根据选中的标签过滤购物车项
    val filteredCartItems = remember(cartItems, selectedTabIndex, currentCartItem) {
        filterCartItemsByTabIndex(cartItems, cartTypes, selectedTabIndex)
    }

    // 计算选中项的总价
    val totalPrice = remember(filteredCartItems) {
        filteredCartItems.sumOf { it.totalPayment }
    }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("购物车") },
        containerColor = dialogContainerColor(),
        text = {
            CartDialogContent(
                filteredCartItems = filteredCartItems,
                cartTypes = cartTypes,
                selectedTabIndex = selectedTabIndex,
                onTabSelected = { newIndex ->
                    // 先更新索引
                    selectedTabIndex = newIndex
                    // 基于新索引过滤购物车项
                    val newFilteredItems = filterCartItemsByTabIndex(cartItems, cartTypes, newIndex)
                    // 选择过滤后的第一项
                    onCartItemSelect(newFilteredItems.firstOrNull())
                },
                onItemClick = onItemClick,
                onAddItem = onAddItem,
                onReduceItem = onReduceItem,
                onDeleteItem = onDeleteItem,
                onItemLongClick = onItemLongClick,
                onSaveProductConfig = onSaveProductConfig,
                onProductSearch = onProductSearch,
                onRefresh = onRefresh,
                onMonitoringTaskDialogShow = { showMonitoringDialog = true }
            )
        },
        confirmButton = {
            // 将CartBottomBar显示在confirmButton位置，使用当前选中标签对应的商品总价
            /*CartBottomBar(
                totalPrice = totalPrice,
                onCheckout = onCheckout
            )*/
        },
        dismissButton = null
    )

    // 监控任务弹窗
    if (showMonitoringDialog) {
        MonitoringTaskDialog(
            onDismiss = { showMonitoringDialog = false },
            onShowAddDialog = { showAddMonitoringDialog = true }
        )
    }

    // 添加监控计划弹窗
    if (showAddMonitoringDialog) {
        AddMonitoringPlanDialog(
            onDismiss = { showAddMonitoringDialog = false },
            onPlanAdded = {
                // 刷新监控计划列表
                scope.launch {
                    try {
                        // 这里可以添加刷新逻辑，比如通知MonitoringTaskDialog刷新
                        // 或者发送事件给ViewModel
                    } catch (e: Exception) {
                        // 处理错误
                    }
                }
            },
            availableAccounts = availableAccounts,
            currentCartItem = currentCartItem
        )
    }
}

/**
 * 根据选中的标签索引过滤购物车项
 *
 * @param cartItems 所有购物车项
 * @param cartTypes 购物车类型列表
 * @param tabIndex 当前选中的标签索引
 * @return 过滤后的购物车项列表
 */
private fun filterCartItemsByTabIndex(
    cartItems: List<CartItem>,
    cartTypes: List<String>,
    tabIndex: Int
): List<CartItem> {
    val targetType = when {
        cartItems.isEmpty() -> return emptyList()
        cartTypes.isEmpty() -> return cartItems
        tabIndex < 0 || tabIndex >= cartTypes.size -> return emptyList()
        else -> {
            when (cartTypes[tabIndex]) {
                "正常" -> "normal"
                "预售" -> "presale"
                else -> return emptyList()
            }
        }
    }

    return cartItems.filter { it.type == targetType }
}

/**
 * 购物车对话框内容
 */
@Composable
private fun CartDialogContent(
    filteredCartItems: List<CartItem>,
    cartTypes: List<String>,
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    onItemClick: (Product) -> Unit,
    onAddItem: (Product) -> Unit,
    onReduceItem: (Product) -> Unit,
    onDeleteItem: (Product) -> Unit,
    onItemLongClick: (Product) -> Unit,
    onSaveProductConfig: (String) -> Unit = {},
    onProductSearch: () -> Unit = {},
    onRefresh: () -> Unit = {},
    onMonitoringTaskDialogShow: () -> Unit = {}
) {
    // 将购物车项转换为模型包装器列表
    val cartModels = remember(filteredCartItems) {
        val models = mutableListOf<CartModelWrapper>()

        filteredCartItems.forEach { cartItem ->
            // 添加店铺名称
            models.add(
                CartModelWrapper(
                    data = cartItem.shopname,
                    modelType = CartModelTypes.SHOP_NAME
                )
            )

            // 添加model信息
            cartItem.cartModels.forEach { model ->
                when (model.modelType) {
                    CartModelTypes.DELIVERY_INFO -> {
                        models.add(
                            CartModelWrapper(
                                data = model.data,
                                modelType = model.modelType
                            )
                        )
                    }

                    CartModelTypes.PROMOTION_INFO -> {
                        models.add(
                            CartModelWrapper(
                                data = model.data,
                                modelType = model.modelType
                            )
                        )
                    }

                    CartModelTypes.PROMOTION_ACTION -> {
                        models.add(
                            CartModelWrapper(
                                data = model.data,
                                modelType = model.modelType
                            )
                        )
                    }

                    CartModelTypes.PRODUCT_ITEM -> {
                        models.add(
                            CartModelWrapper(
                                data = model.data,
                                modelType = model.modelType
                            )
                        )
                    }
                }
            }
        }

        models
    }

    Column(modifier = Modifier.heightIn(max = 500.dp)) {
        // 购物车类型标签
        if (cartTypes.size > 1) {
            // 使用TabBar组件代替原来的标签栏实现
            val tabs = remember(cartTypes) {
                cartTypes.map { TabItem(name = it) }
            }

            TabBar(
                tabs = tabs,
                selectedTabIndex = selectedTabIndex,
                onTabSelected = { index ->
                    onTabSelected(index)
                }
            )
        }

        Card(
            colors = cardThemeOverlay(),
            modifier = Modifier
                .padding(top = 2.dp, bottom = 2.dp)
                .fillMaxWidth()
        ) {
            Row {
                TokenActionButton(
                    icon = R.drawable.baseline_save_24,
                    text = "商品配置",
                    onClick = {
                        // 从购物车中收集所有商品的配置信息
                        val productConfigs = collectProductConfig(cartModels)

                        // 调用回调保存配置
                        if (productConfigs.isNotEmpty()) {
                            onSaveProductConfig(productConfigs)
                        }
                    }
                )

                TokenActionButton(
                    icon = R.drawable.outline_monitoring_24,
                    text = "库存监控",
                    onClick = {
                        onMonitoringTaskDialogShow()
                    }
                )

                TokenActionButton(
                    icon = R.drawable.baseline_search_24,
                    text = "商品搜索",
                    onClick = onProductSearch
                )

                TokenActionButton(
                    imageVector = Icons.Filled.Refresh,
                    text = "刷新",
                    onClick = onRefresh
                )
            }
        }

        // 购物车内容
        if (cartModels.isEmpty()) {
            EmptyCartContent(
                modifier = Modifier
                    .padding(vertical = 0.dp)
            )
        } else {
            LazyColumn(
                modifier = Modifier
                    .padding(vertical = 4.dp)
            ) {
                items(cartModels) { model ->
                    CartModelItem(
                        cartModel = model,
                        onItemClick = onItemClick,
                        onAddItem = onAddItem,
                        onReduceItem = onReduceItem,
                        onDeleteItem = onDeleteItem,
                        onItemLongClick = onItemLongClick
                    )
                }
            }
        }
    }
}

/**
 * 收集购物车商品配置信息
 * 从购物车模型列表中提取所有商品，生成格式为"id,num[,goodstagid][,bundlepromocode];"的配置字符串
 * 合并具有相同id,tagId,promoCode的商品，将它们的数量相加
 * 需要兑换的商品(goodstagid=3)会被放在列表末尾
 *
 * @param cartModels 购物车模型列表
 * @return 商品配置字符串
 */
fun collectProductConfig(cartModels: List<CartModelWrapper>): String {
    // 收集所有选中的商品
    val selectedProducts = mutableListOf<Product>()

    // 从购物车模型中提取所有选中的商品
    cartModels.forEach { model ->
        if (model.modelType == CartModelTypes.PRODUCT_ITEM) {
            val product = model.getProduct()
            if (product != null && product.selectstate == 1) {
                selectedProducts.add(product)
            }
        }
    }

    // 如果没有选中的商品，返回空字符串
    if (selectedProducts.isEmpty()) {
        return ""
    }

    // 创建一个映射来存储合并后的商品信息
    val mergedProducts = mutableMapOf<String, Int>()

    selectedProducts.forEach { product ->
        val id = product.originalskucode.ifEmpty { product.id }
        val num = product.num / 100
        val promoCode = product.bundlepromocode.ifEmpty { "NO-CODE" }
        val key = "$id,${product.goodstagid},$promoCode"

        // 如果已存在相同的key，则累加数量；否则创建新条目
        mergedProducts[key] = mergedProducts.getOrDefault(key, 0) + num
    }

    // 将合并后的结果转换为最终格式
    return mergedProducts
        .map { (key, totalNum) ->
            val parts = key.split(",")
            val id = parts[0]
            val tagId = parts[1]
            val promoCode = parts[2]
            "$id,$totalNum,$tagId,$promoCode"
        }
        .sortedBy { it.split(",")[2] == "3" } // 将需要兑换的商品(goodsTagId=3)排到最后
        .joinToString(";") + ";"
}

/**
 * 购物车底部栏
 */
@Composable
fun CartBottomBar(
    totalPrice: Int,
    onCheckout: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp, vertical = 2.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = "合计: ",
            fontSize = 16.sp
        )

        Text(
            text = "¥${formatPrice(totalPrice)}",
            fontSize = 18.sp,
            color = Color.Red,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.weight(1f))

        Button(
            onClick = onCheckout,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color.Red
            ),
            shape = RoundedCornerShape(20.dp),
            modifier = Modifier.padding(start = 8.dp)
        ) {
            Text("去结算")
        }
    }
}

/**
 * 空购物车内容
 */
@Composable
fun EmptyCartContent(modifier: Modifier = Modifier) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
    }
}

/**
 * 格式化价格（分转元）
 */
private fun formatPrice(priceInCents: Int): String {
    val df = DecimalFormat("0.00")
    return df.format(priceInCents / 100.0)
}

/**
 * 格式化数量
 */
private fun formatNum(num: Int): String {
    val df = DecimalFormat("0")
    return df.format(num / 100)
}
